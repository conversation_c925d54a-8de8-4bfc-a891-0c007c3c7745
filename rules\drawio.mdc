description: Draw.io 图表绘制指南
globs: *.drawio
alwaysApply: false

# Draw.io 图表绘制指南

## 基本规范

### 文件命名格式
`[模块名][图表类型][版本].drawio`
- 示例: `ai assistant architecture v1.drawio`

### 组件颜色标准
- **AI/LLM组件**: 浅蓝色填充(#dae8fc) + 蓝色边框(#6c8ebf)
- **知识库/数据**: 浅黄色填充(#fff2cc) + 黄色边框(#d6b656)
- **基础服务**: 浅灰色填充(#f5f5f5) + 灰色边框(#666666)
- **用户/外部**: 白色填充 + 黑色边框
- **连接线**: 黑色实线，重要流程用粗线(strokeWidth=2)

### 组件形状和样式
- **标准组件**: 圆角矩形，带阴影效果
- **分组容器**: 大圆角矩形，浅色填充作背景
- **连接线**: 直角连接，避免斜线交叉
- **标签**: 简洁中文描述，关键信息用粗体

## 绘制原则

- **分层布局**: 严格按层次从左到右排列(客户端 > 服务端 > AI服务 > 外部系统)
- **模块分组**: 用大容器框架分组相关组件，添加模块标题
- **流程清晰**: 主流程用粗实线，返回/异步用虚线
- **对齐整齐**: 同层组件水平对齐，连接线保持直角

## 标准架构模板

### AI系统架构图布局
```
[客户端层] > [服务端层] > [AI服务层] > [AI引擎层]
用户界面 > 业务服务 > LLM模型 > AI接口 > 路由选择Agent > 知识库
```

### 组件配置规范
- **模块容器**: 宽度550px，高度270px，浅灰色背景
- **标准组件**: 宽度120px，高度60px，带阴影
- **连接线**: strokeWidth=2，使用orthogonal样式
- **标签位置**: 连接线上方或旁边，避免重叠

### 流程线规范
- **请求流程**: 黑色实线，箭头指向
- **响应流程**: 黑色虚线，箭头指向
- **数据流**: 加粗实线(strokeWidth=2)
- **异步调用**: 虚线(dashed=1)
- **避免重叠**: 调整连接点位置，使用不同的坐标路径
- **线条分离**: 实线和虚线使用不同路径，相距至少20px

## 版本管理

### 版本命名规范
**格式**: `[模块名][图表类型]_v[版本号]_[今天日期].drawio`

**示例**: `aiconf_system_flow_v1.1_20250619.drawio`

**版本号规则**:
- v1.0: 初始版本
- v1.1: 小修改(组件调整、样式优化)
- v2.0: 重大修改(架构变更、模块重构)

### 标准工作流程

1. **复制原文件创建副本**:
```bash
cp [原文件名].drawio [原文件名]_v[新版本号]_[今天日期].drawio
```

示例:
```bash
cp aiconf_system_flow_v1.0_20250619.drawio aiconf_system_flow_v1.1_20250619.drawio
```

2. **在副本上进行修改**: 对新创建的副本文件进行所有必要的修改

3. **逐步确认修改完成**:
   - 检查所有用户要求是否已满足
   - 验证图表可以正常打开和显示
   - 确认修改质量符合标准

4. **更新主文件**:
```bash
cp [副本文件名].drawio [主文件名].drawio
```

5. **更新CHANGELOG**: 在CHANGELOG.md中记录新版本变更

### 修改日志格式

```markdown
## [版本号] - 日期

### 新增(Added)
- **文件名**: 简洁描述主要功能

### 修复(Fixed)
- **问题描述**: 修复的具体问题

### 技术细节
- **关键变更**: 重要的技术实现细节
```

### 示例操作流程

```bash
# 1. 复制原文件创建副本
cp aiconf_system_flow_v1.0_20250619.drawio aiconf_system_flow_v1.1_20250619.drawio

# 2. 在副本上进行修改
# 编辑 aiconf_system_flow_v1.1_20250619.drawio

# 3. 在CHANGELOG.md中添加新版本记录
# 格式: ## [v1.1] - 2025-06-19
# ### 修复(Fixed)
# - **问题描述**: 具体修复内容
```

## 最佳实践

- **先复制再修改**: 始终在副本上进行修改，避免直接修改原文件
- **逐步确认**: 修改完成后逐步检查是否满足所有要求
- **简洁记录**: CHANGELOG只记录关键变更，避免冗余信息
- **保留历史**: 不删除旧版本文件，建立版本历史
